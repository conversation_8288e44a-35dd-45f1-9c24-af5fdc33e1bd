// const scheduleJobStatusUpdate = require("../worker/updateCSVData");
// const reviewChecker = require("../worker/lexImageGenWorker");

// // const processLexReviewJobs = require("./lexReviewsWorker");
// require("./bullWorker");
require("dotenv").config();
const AutoScalingWorker = require("./autoScalingWorker");

// Initialize AmazonProductData sync worker
if (process.env.ENABLE_AMAZON_SYNC === 'true') {
  const { initializeSyncJob } = require("./amazonProductDataSyncWorker");
  console.log("🔄 Initializing AmazonProductData sync worker...");
  initializeSyncJob();
}

if (process.env.SERVER_ID === "Main") {
  const autoScalingWorker = new AutoScalingWorker();
  autoScalingWorker.start();
}

// Initialize and start the Lex Worker Manager
if (process.env.SERVER_QUEUE === "lexQueue") {
  const LexWorkerManager = require("./lexWorkerManager");
  const lexWorkerManager = new LexWorkerManager();
  require("./lexWeeklyAsinChecker");

  // daily worker
  // require("./lexReviewCheckerWorker");

  console.log("🚀 Starting Scheduler with Lex Worker Manager...");

  // Start the workers
  lexWorkerManager.start().catch(console.error);

  // Graceful shutdown handling
  const gracefulShutdown = async (signal) => {
    console.log(`\n🔄 Received ${signal}, initiating graceful shutdown...`);

    try {
      await lexWorkerManager.stop();
      console.log("👋 Scheduler shutdown complete");
      process.exit(0);
    } catch (error) {
      console.error("❌ Error during scheduler shutdown:", error);
      process.exit(1);
    }
  };

  // Handle shutdown signals
  process.on("SIGINT", () => gracefulShutdown("SIGINT"));
  process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));

  // Handle uncaught exceptions
  process.on("uncaughtException", (error) => {
    console.error("💥 Uncaught Exception in scheduler:", error);
    gracefulShutdown("uncaughtException");
  });

  process.on("unhandledRejection", (reason, promise) => {
    console.error(
      "💥 Unhandled Rejection in scheduler at:",
      promise,
      "reason:",
      reason
    );
    gracefulShutdown("unhandledRejection");
  });
}
// const scheduleJobStatusUpdate = require("../worker/updateCSVData");
// const reviewChecker = require("../worker/lexImageGenWorker");
// const processLexJobs = require('./lexWorker')
// const processLexReviewJobs = require('./lexReviewsWorker')
// const processAiAnalysisJobs = require('./lexAiAnalysisWorker')
require("./bullWorker");